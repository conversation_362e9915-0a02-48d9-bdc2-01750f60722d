/**
 * SaveServiceHelper 配置文件
 * <AUTHOR>
 * @date 2025-08-01
 */

export interface SaveServiceConfig {
    // 实体配置
    entity: {
        name: string;           // 实体名称
        primaryKey: string;     // 主键字段名
        statusField: string;    // 状态字段名
    };
    
    // 状态值配置
    statusValues: {
        closed: string;         // 关闭状态值
        open: string;           // 打开状态值
    };
    
    // 更新选项
    updateOptions: {
        validateBusiness: boolean;    // 是否进行业务验证
        triggerWorkflow: boolean;     // 是否触发工作流
        updateVersion: boolean;       // 是否更新版本号
        batchSize: number;           // 批处理大小
    };
    
    // 额外字段配置
    extraFields: {
        closeDate: string;      // 关闭日期字段名
        closeUser: string;      // 关闭人字段名
        closeRemark: string;    // 关闭备注字段名
    };
}

// 默认配置
export const defaultSaveServiceConfig: SaveServiceConfig = {
    entity: {
        name: "your_entity_name",    // 需要替换为实际实体名
        primaryKey: "fid",           // 通常kingscript使用fid作为主键
        statusField: "fbillstatus"   // 通常单据状态字段为fbillstatus
    },
    statusValues: {
        closed: "C",                 // 关闭状态通常用C表示
        open: "A"                    // 打开状态通常用A表示
    },
    updateOptions: {
        validateBusiness: true,      // 默认进行业务验证
        triggerWorkflow: false,      // 默认不触发工作流
        updateVersion: true,         // 默认更新版本号
        batchSize: 10               // 默认批处理大小
    },
    extraFields: {
        closeDate: "fclosedate",     // 关闭日期字段
        closeUser: "fcloseuser",     // 关闭人字段
        closeRemark: "fcloseremark"  // 关闭备注字段
    }
};

// 常见实体配置预设
export const commonEntityConfigs = {
    // 应付单
    ap_bill: {
        entity: {
            name: "ap_bill",
            primaryKey: "fid",
            statusField: "fbillstatus"
        },
        statusValues: {
            closed: "C",
            open: "A"
        }
    },
    
    // 应收单
    ar_bill: {
        entity: {
            name: "ar_bill", 
            primaryKey: "fid",
            statusField: "fbillstatus"
        },
        statusValues: {
            closed: "C",
            open: "A"
        }
    },
    
    // 物料基础资料
    bd_material: {
        entity: {
            name: "bd_material",
            primaryKey: "fid", 
            statusField: "fstatus"
        },
        statusValues: {
            closed: "B",  // 禁用
            open: "A"     // 启用
        }
    },
    
    // 客户基础资料
    bd_customer: {
        entity: {
            name: "bd_customer",
            primaryKey: "fid",
            statusField: "fstatus"
        },
        statusValues: {
            closed: "B",
            open: "A"
        }
    },
    
    // 供应商基础资料
    bd_supplier: {
        entity: {
            name: "bd_supplier",
            primaryKey: "fid",
            statusField: "fstatus"
        },
        statusValues: {
            closed: "B",
            open: "A"
        }
    }
};

// 工具函数：获取指定实体的配置
export function getEntityConfig(entityName: string): Partial<SaveServiceConfig> {
    return commonEntityConfigs[entityName] || {};
}

// 工具函数：合并配置
export function mergeConfig(baseConfig: SaveServiceConfig, customConfig: Partial<SaveServiceConfig>): SaveServiceConfig {
    return {
        entity: { ...baseConfig.entity, ...customConfig.entity },
        statusValues: { ...baseConfig.statusValues, ...customConfig.statusValues },
        updateOptions: { ...baseConfig.updateOptions, ...customConfig.updateOptions },
        extraFields: { ...baseConfig.extraFields, ...customConfig.extraFields }
    };
}

// 工具函数：构造更新数据
export function buildUpdateData(
    id: string, 
    config: SaveServiceConfig, 
    extraData?: { [key: string]: any }
): any {
    let updateData: any = {};
    
    // 设置主键
    updateData[config.entity.primaryKey] = id;
    
    // 设置状态
    updateData[config.entity.statusField] = config.statusValues.closed;
    
    // 设置额外字段
    if (config.extraFields.closeDate) {
        updateData[config.extraFields.closeDate] = new Date();
    }
    
    // 合并额外数据
    if (extraData) {
        Object.assign(updateData, extraData);
    }
    
    return updateData;
}

// 工具函数：构造批量更新数据
export function buildBatchUpdateData(
    ids: string[], 
    config: SaveServiceConfig,
    extraData?: { [key: string]: any }
): any[] {
    return ids.map(id => buildUpdateData(id, config, extraData));
}

// 使用示例配置
export const exampleConfigs = {
    // 示例1：自定义单据
    customBill: {
        entity: {
            name: "custom_bill",
            primaryKey: "fid",
            statusField: "fbillstatus"
        },
        statusValues: {
            closed: "CLOSED",
            open: "OPEN"
        },
        extraFields: {
            closeDate: "fclosedate",
            closeUser: "fcloseuser",
            closeRemark: "fremark"
        }
    },
    
    // 示例2：工作流单据
    workflowBill: {
        entity: {
            name: "workflow_bill",
            primaryKey: "id",
            statusField: "status"
        },
        statusValues: {
            closed: "4",  // 数字状态
            open: "1"
        },
        updateOptions: {
            validateBusiness: true,
            triggerWorkflow: true,  // 触发工作流
            updateVersion: true,
            batchSize: 5
        }
    }
};
