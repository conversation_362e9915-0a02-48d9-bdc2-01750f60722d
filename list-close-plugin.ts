/**
 * <AUTHOR>
 * @date 2025-08-01
 * @description 列表界面关闭按钮插件 - 获取选中行并更新单据状态为关闭
 */
import { AbstractListPlugin } from "@cosmic/bos-core/kd/bos/list/plugin";
import { SetFilterEvent, FilterContainerSearchClickArgs } from "@cosmic/bos-core/kd/bos/form/events";
import { ItemClickEvent } from "@cosmic/bos-core/kd/bos/form/control/events";
import { EventObject } from "@cosmic/bos-script/java/util";
import { Toolbar } from "@cosmic/bos-core/kd/bos/form/control";
import { RequestContext } from "@cosmic/bos-core/kd/bos/context";
import { OperationResult } from "@cosmic/bos-core/kd/bos/servicehelper";
import { PluginConfig, defaultConfig, formatMessage, deduplicateIds } from "./plugin-config";

class MyPlugin extends AbstractListPlugin {

    // 插件配置，可以在构造函数中传入自定义配置
    private config: PluginConfig;

    constructor(customConfig?: Partial<PluginConfig>) {
        super();
        // 合并默认配置和自定义配置
        this.config = { ...defaultConfig, ...customConfig };
    }

    setFilter(e: SetFilterEvent): void {
        super.setFilter(e);
        //...
    }

    filterContainerSearchClick(e: FilterContainerSearchClickArgs): void {
        super.filterContainerSearchClick(e);
        //...
    }

    itemClick(e: ItemClickEvent): void {
        super.itemClick(e);

        let key = e.getItemKey();
        if("yg_close" == key) {
            this.closeSelectedBills();
        }
    }

    /**
     * 关闭选中的单据
     */
    private closeSelectedBills(): void {
        try {
            // 获取选中的行
            var selectedRows = this.getView().getSelectedRows();

            if (!selectedRows || selectedRows.size() === 0) {
                this.getView().showMessage(this.config.messages.noSelection);
                return;
            }

            // 验证选中的行是否可以关闭
            let validation = this.validateSelectedRows(selectedRows);
            if (!validation.valid) {
                this.getView().showMessage(validation.message);
                return;
            }

            // 确认操作
            let confirmMessage = formatMessage(this.config.messages.confirmClose, { count: selectedRows.size() });
            let confirmResult = this.getView().showConfirm(confirmMessage);
            if (!confirmResult) {
                return;
            }

            // 收集要关闭的单据ID并去重
            let deduplicateResult = deduplicateIds(selectedRows, this.config);
            let { billIds, billNos, originalCount, uniqueCount, duplicateCount } = deduplicateResult;

            // 去重后的数组长度检查
            if (billIds.length === 0) {
                this.getView().showMessage(this.config.messages.noValidIds);
                return;
            }

            // 如果启用了去重且有重复项，提示用户
            if (this.config.deduplication.enabled &&
                this.config.deduplication.showDuplicateMessage &&
                duplicateCount > 0) {
                let duplicateMessage = formatMessage(this.config.messages.duplicateDetected, {
                    duplicateCount: duplicateCount,
                    uniqueCount: uniqueCount
                });
                this.getView().showMessage(duplicateMessage);
            }

            // 调用后端服务关闭单据
            this.closeBillsOnServer(billIds, billNos);

        } catch (error) {
            console.error("关闭单据时发生错误:", error);
            this.getView().showErrorMessage("关闭单据失败: " + error.message);
        }
    }

    /**
     * 调用后端服务关闭单据
     */
    private closeBillsOnServer(billIds: string[], billNos: string[]): void {
        try {
            // 显示加载提示
            this.getView().showWaitDialog(this.config.messages.loadingMessage);

            // 创建请求上下文
            let ctx = new RequestContext();

            // 设置要关闭的单据参数
            let params = {
                "ids": billIds,
                "billNos": billNos,
                "status": this.config.statusValues.closed,
                "closeDate": new Date().toISOString(), // 关闭日期
                "operator": ctx.getUserId() // 操作人
            };

            // 调用后端服务
            let serviceName = this.config.service.name;
            let operationName = this.config.service.operation;

            // 异步调用服务
            this.getView().invokeOperation(serviceName, operationName, params, (result: OperationResult) => {
                // 隐藏加载提示
                this.getView().hideWaitDialog();

                if (result.isSuccess()) {
                    let successMessage = formatMessage(this.config.messages.successMessage, { count: billIds.length });
                    this.getView().showMessage(successMessage);
                    // 刷新列表
                    this.getView().refresh();
                    // 清除选择
                    this.getView().clearSelection();
                } else {
                    let errorMsg = result.getMessage() || "未知错误";
                    let errorMessage = formatMessage(this.config.messages.errorMessage, { error: errorMsg });
                    this.getView().showErrorMessage(errorMessage);
                    console.error("关闭单据失败:", result);
                }
            }, (error: any) => {
                // 处理网络错误或其他异常
                this.getView().hideWaitDialog();
                console.error("调用服务异常:", error);
                this.getView().showErrorMessage("网络错误，请检查网络连接后重试");
            });

        } catch (error) {
            this.getView().hideWaitDialog();
            console.error("调用后端服务时发生错误:", error);
            this.getView().showErrorMessage("调用服务失败: " + error.message);
        }
    }

    /**
     * 验证选中行是否可以关闭
     */
    private validateSelectedRows(selectedRows: any): { valid: boolean, message?: string } {
        for (let i = 0; i < selectedRows.size(); i++) {
            let row = selectedRows.get(i);
            let status = row.getString(this.config.fields.status);

            // 检查单据状态，如果已经是关闭状态则不能再次关闭
            if (status === this.config.statusValues.closed || status === this.config.statusValues.closedDisplay) {
                let billNo = row.getString(this.config.fields.billNo) || row.getString(this.config.fields.id);
                let message = formatMessage(this.config.messages.alreadyClosed, { billNo: billNo });
                return {
                    valid: false,
                    message: message
                };
            }

            // 可以添加其他业务规则验证
            // 例如：检查是否有权限关闭、是否满足关闭条件等
        }

        return { valid: true };
    }

    // Listen for menu button click events.
    registerListener(e: EventObject): void {
        super.registerListener(e);

        // 监听工具栏
        let toolbar = this.getView().getControl("toolbarap") as Toolbar;
        toolbar.addItemClickListener(this);
    }
}

// 创建插件实例，可以传入自定义配置
let plugin = new MyPlugin({
    // 如果需要自定义配置，可以在这里覆盖默认值
    // 例如：
    // fields: {
    //     id: "fid",
    //     billNo: "fbillno",
    //     status: "fstatus"
    // },
    // service: {
    //     name: "MyCustomBillService",
    //     operation: "closeMyBills"
    // }
});

export { plugin };
