/**
 * 最终的去重解决方案 - 直接集成到你的原始代码中
 * <AUTHOR>
 * @date 2025-08-01
 */
import { AbstractListPlugin } from "@cosmic/bos-core/kd/bos/list/plugin";
import { SetFilterEvent, FilterContainerSearchClickArgs } from "@cosmic/bos-core/kd/bos/form/events";
import { ItemClickEvent } from "@cosmic/bos-core/kd/bos/form/control/events";
import { EventObject } from "@cosmic/bos-script/java/util";

class MyPlugin extends AbstractListPlugin {

    setFilter(e: SetFilterEvent): void {
        super.setFilter(e);
        //...
    }

    filterContainerSearchClick(e: FilterContainerSearchClickArgs): void {
        super.filterContainerSearchClick(e);
        //...
    }

    itemClick(e: ItemClickEvent): void {
        super.itemClick(e);

        let key = e.getItemKey();
        if("yg_close" == key) {

            var listModel = this.getView().getModel();
            var listV = this.getView().getSelectedRows();
            console.log("原始选中行数:", listV.size());

            if (!listV || listV.size() === 0) {
                plugin.getView().showMessage("请先选择要关闭的单据!");
                return;
            }

            // 去重处理
            let deduplicationResult = this.deduplicateSelectedRows(listV);
            
            if (deduplicationResult.uniqueIds.length === 0) {
                plugin.getView().showMessage("未找到有效的单据ID!");
                return;
            }

            // 如果有重复项，提示用户
            if (deduplicationResult.duplicateCount > 0) {
                let message = `检测到 ${deduplicationResult.duplicateCount} 条重复记录，已自动去重。将处理 ${deduplicationResult.uniqueIds.length} 条唯一记录。`;
                plugin.getView().showMessage(message);
            }

            // 确认操作
            let confirmMessage = `确定要关闭选中的 ${deduplicationResult.uniqueIds.length} 条单据吗？`;
            if (!plugin.getView().showConfirm(confirmMessage)) {
                return;
            }

            // 调用关闭逻辑
            this.closeBillsWithIds(deduplicationResult.uniqueIds, deduplicationResult.uniqueBillNos);
        }
    }

    /**
     * 对选中行进行去重处理
     */
    private deduplicateSelectedRows(listV: any): {
        uniqueIds: string[],
        uniqueBillNos: string[],
        duplicateCount: number,
        originalCount: number
    } {
        let uniqueIds: string[] = [];
        let uniqueBillNos: string[] = [];
        let processedIds = new Set<string>();
        let duplicateCount = 0;

        for (var i = 0; i < listV.size(); i++) {
            let row = listV.get(i);
            
            // 获取ID和单据号 - 请根据你的实际字段名修改
            let billId = row.getString("id");        // 主键字段
            let billNo = row.getString("billno");    // 单据号字段
            
            if (billId) {
                if (processedIds.has(billId)) {
                    // 发现重复ID
                    duplicateCount++;
                    console.log(`发现重复ID: ${billId}, 单据号: ${billNo}`);
                } else {
                    // 新的唯一ID
                    processedIds.add(billId);
                    uniqueIds.push(billId);
                    uniqueBillNos.push(billNo || billId);
                }
            }
        }

        console.log(`去重统计: 原始 ${listV.size()} 条，唯一 ${uniqueIds.length} 条，重复 ${duplicateCount} 条`);

        return {
            uniqueIds,
            uniqueBillNos,
            duplicateCount,
            originalCount: listV.size()
        };
    }

    /**
     * 使用去重后的ID关闭单据
     */
    private closeBillsWithIds(billIds: string[], billNos: string[]): void {
        try {
            console.log("准备关闭的单据IDs:", billIds);
            console.log("准备关闭的单据号:", billNos);

            // 显示加载提示
            plugin.getView().showWaitDialog("正在关闭单据，请稍候...");

            // 准备参数
            let params = {
                "ids": billIds,
                "billNos": billNos,
                "status": "closed",
                "closeDate": new Date().toISOString(),
                "operator": "current_user" // 可以从上下文获取当前用户
            };

            // 调用后端服务 - 请根据你的实际服务名修改
            let serviceName = "YourBillService";
            let operationName = "closeBills";

            plugin.getView().invokeOperation(serviceName, operationName, params, (result: any) => {
                plugin.getView().hideWaitDialog();
                
                if (result.isSuccess && result.isSuccess()) {
                    plugin.getView().showMessage(`成功关闭 ${billIds.length} 条单据!`);
                    // 刷新列表
                    plugin.getView().refresh();
                    // 清除选择
                    if (plugin.getView().clearSelection) {
                        plugin.getView().clearSelection();
                    }
                } else {
                    let errorMsg = result.getMessage ? result.getMessage() : "未知错误";
                    plugin.getView().showErrorMessage(`关闭单据失败: ${errorMsg}`);
                }
            }, (error: any) => {
                plugin.getView().hideWaitDialog();
                console.error("调用服务异常:", error);
                plugin.getView().showErrorMessage("网络错误，请检查网络连接后重试");
            });

        } catch (error) {
            plugin.getView().hideWaitDialog();
            console.error("关闭单据时发生错误:", error);
            plugin.getView().showErrorMessage("操作失败: " + error.message);
        }
    }

    // Listen for menu button click events.
    registerListener(e: EventObject): void {
        super.registerListener(e);

        // 监听工具栏
        let toolbar = this.getView().getControl("toolbarap") as Toolbar;
        toolbar.addItemClickListener(this);
    }
}

let plugin = new MyPlugin();

export { plugin };

// 额外的工具函数，可以在其他地方使用
export class DeduplicationUtils {
    
    /**
     * 通用的数组去重函数
     */
    static deduplicateArray<T>(array: T[], keySelector: (item: T) => string): {
        unique: T[],
        duplicates: T[],
        stats: { original: number, unique: number, duplicate: number }
    } {
        let seen = new Set<string>();
        let unique: T[] = [];
        let duplicates: T[] = [];

        for (let item of array) {
            let key = keySelector(item);
            if (seen.has(key)) {
                duplicates.push(item);
            } else {
                seen.add(key);
                unique.push(item);
            }
        }

        return {
            unique,
            duplicates,
            stats: {
                original: array.length,
                unique: unique.length,
                duplicate: duplicates.length
            }
        };
    }

    /**
     * 字符串数组去重
     */
    static deduplicateStrings(strings: string[]): string[] {
        return [...new Set(strings)];
    }

    /**
     * 对象数组按指定字段去重
     */
    static deduplicateByField<T>(array: T[], fieldName: keyof T): T[] {
        let seen = new Set();
        return array.filter(item => {
            let value = item[fieldName];
            if (seen.has(value)) {
                return false;
            }
            seen.add(value);
            return true;
        });
    }
}
