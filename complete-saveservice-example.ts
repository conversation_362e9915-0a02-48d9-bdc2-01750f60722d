/**
 * 完整的 SaveServiceHelper.update 示例 - 使用配置化方式
 * <AUTHOR>
 * @date 2025-08-01
 */
import { AbstractListPlugin } from "@cosmic/bos-core/kd/bos/list/plugin";
import { SetFilterEvent, FilterContainerSearchClickArgs } from "@cosmic/bos-core/kd/bos/form/events";
import { ItemClickEvent } from "@cosmic/bos-core/kd/bos/form/control/events";
import { EventObject } from "@cosmic/bos-script/java/util";
import { SaveServiceHelper } from "@cosmic/bos-core/kd/bos/servicehelper";
import { RequestContext } from "@cosmic/bos-core/kd/bos/context";
import { SaveServiceConfig, defaultSaveServiceConfig, buildUpdateData, mergeConfig } from "./saveservice-config";

class MyPlugin extends AbstractListPlugin {
    
    // 插件配置
    private config: SaveServiceConfig;
    
    constructor(customConfig?: Partial<SaveServiceConfig>) {
        super();
        // 合并默认配置和自定义配置
        this.config = mergeConfig(defaultSaveServiceConfig, customConfig || {});
    }

    setFilter(e: SetFilterEvent): void {
        super.setFilter(e);
        //...
    }

    filterContainerSearchClick(e: FilterContainerSearchClickArgs): void {
        super.filterContainerSearchClick(e);
        //...
    }

    itemClick(e: ItemClickEvent): void {
        super.itemClick(e);

        let key = e.getItemKey();
        if("yg_close" == key) {

            var listModel = this.getView().getModel();
            var listV = this.getView().getSelectedRows();
            console.log("选中行数量:", listV.size());

            if (!listV || listV.size() === 0) {
                plugin.getView().showMessage("请先选择要关闭的单据!");
                return;
            }

            // 去重并收集ID
            let uniqueIds = this.collectUniqueIds(listV);
            
            if (uniqueIds.length === 0) {
                plugin.getView().showMessage("未找到有效的单据ID!");
                return;
            }

            // 确认操作
            if (!plugin.getView().showConfirm(`确定要关闭选中的 ${uniqueIds.length} 条单据吗？`)) {
                return;
            }

            // 使用 SaveServiceHelper.update 更新状态
            this.updateBillStatusBatch(uniqueIds);
        }
    }

    /**
     * 收集唯一的ID
     */
    private collectUniqueIds(listV: any): string[] {
        let uniqueIds = new Set<string>();
        
        for(var i = 0; i < listV.size(); i++){
            let row = listV.get(i);
            let billId = row.getString(this.config.entity.primaryKey);
            if (billId) {
                uniqueIds.add(billId);
            }
        }
        
        return Array.from(uniqueIds);
    }

    /**
     * 批量更新单据状态
     */
    private updateBillStatusBatch(billIds: string[]): void {
        try {
            plugin.getView().showWaitDialog("正在关闭单据，请稍候...");

            let ctx = new RequestContext();
            let successCount = 0;
            let failCount = 0;
            let totalCount = billIds.length;
            let processedCount = 0;

            // 分批处理
            let batchSize = this.config.updateOptions.batchSize;
            let batches = this.chunkArray(billIds, batchSize);

            console.log(`开始批量更新，总计 ${totalCount} 条记录，分 ${batches.length} 批处理`);

            // 处理每一批
            batches.forEach((batch, batchIndex) => {
                this.processBatch(batch, batchIndex, ctx, (success, fail) => {
                    successCount += success;
                    failCount += fail;
                    processedCount += batch.length;

                    // 如果所有批次都处理完成
                    if (processedCount >= totalCount) {
                        plugin.getView().hideWaitDialog();
                        let message = `处理完成！成功: ${successCount} 条，失败: ${failCount} 条`;
                        plugin.getView().showMessage(message);
                        
                        if (successCount > 0) {
                            plugin.getView().refresh();
                        }
                    }
                });
            });

        } catch (error) {
            plugin.getView().hideWaitDialog();
            console.error("批量更新时发生错误:", error);
            plugin.getView().showErrorMessage("操作失败: " + error.message);
        }
    }

    /**
     * 处理单个批次
     */
    private processBatch(
        batch: string[], 
        batchIndex: number, 
        ctx: RequestContext, 
        callback: (success: number, fail: number) => void
    ): void {
        let batchSuccessCount = 0;
        let batchFailCount = 0;
        let batchProcessedCount = 0;

        console.log(`处理第 ${batchIndex + 1} 批，包含 ${batch.length} 条记录`);

        batch.forEach((billId, index) => {
            // 构造更新数据
            let updateData = buildUpdateData(billId, this.config, {
                [this.config.extraFields.closeUser]: ctx.getUserId(),
                [this.config.extraFields.closeRemark]: "批量关闭操作"
            });

            console.log(`更新单据 ${billId}:`, updateData);

            // 调用 SaveServiceHelper.update
            SaveServiceHelper.update(
                this.config.entity.name,
                updateData,
                // 成功回调
                (result: any) => {
                    console.log(`单据 ${billId} 更新成功:`, result);
                    batchSuccessCount++;
                    batchProcessedCount++;
                    
                    // 如果当前批次处理完成
                    if (batchProcessedCount >= batch.length) {
                        callback(batchSuccessCount, batchFailCount);
                    }
                },
                // 失败回调
                (error: any) => {
                    console.error(`单据 ${billId} 更新失败:`, error);
                    batchFailCount++;
                    batchProcessedCount++;
                    
                    // 如果当前批次处理完成
                    if (batchProcessedCount >= batch.length) {
                        callback(batchSuccessCount, batchFailCount);
                    }
                },
                ctx
            );
        });
    }

    /**
     * 将数组分割成指定大小的批次
     */
    private chunkArray<T>(array: T[], chunkSize: number): T[][] {
        let chunks: T[][] = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }

    // Listen for menu button click events.
    registerListener(e: EventObject): void {
        super.registerListener(e);

        // 监听工具栏
        let toolbar = this.getView().getControl("toolbarap") as Toolbar;
        toolbar.addItemClickListener(this);
    }
}

// 创建插件实例，传入自定义配置
let plugin = new MyPlugin({
    // 配置你的实体信息
    entity: {
        name: "your_bill_entity",    // 替换为你的实际实体名
        primaryKey: "fid",           // 替换为你的主键字段名
        statusField: "fbillstatus"   // 替换为你的状态字段名
    },
    statusValues: {
        closed: "C",                 // 替换为你的关闭状态值
        open: "A"                    // 替换为你的打开状态值
    },
    updateOptions: {
        batchSize: 5                 // 每批处理5条记录
    }
});

export { plugin };

/*
使用说明：

1. 基本配置：
   在创建插件实例时，配置你的实体信息：
   - entity.name: 实体名称，如 "ap_bill", "ar_bill", "custom_bill" 等
   - entity.primaryKey: 主键字段名，通常是 "fid" 或 "id"
   - entity.statusField: 状态字段名，如 "fbillstatus", "fstatus" 等

2. 状态值配置：
   - statusValues.closed: 关闭状态的值，如 "C", "CLOSED", "4" 等
   - statusValues.open: 打开状态的值，如 "A", "OPEN", "1" 等

3. SaveServiceHelper.update 的完整语法：
   SaveServiceHelper.update(
       entityName,      // 实体名称
       updateData,      // 更新数据对象 { 字段名: 值 }
       successCallback, // 成功回调 (result) => {}
       errorCallback,   // 失败回调 (error) => {}
       context         // 请求上下文（可选）
   );

4. 更新数据格式：
   {
       "fid": "单据ID",
       "fbillstatus": "C",
       "fclosedate": new Date(),
       "fcloseuser": "用户ID"
   }

5. 常见实体配置示例：
   
   应付单：
   entity: { name: "ap_bill", primaryKey: "fid", statusField: "fbillstatus" }
   statusValues: { closed: "C", open: "A" }
   
   物料：
   entity: { name: "bd_material", primaryKey: "fid", statusField: "fstatus" }
   statusValues: { closed: "B", open: "A" }
   
   自定义单据：
   entity: { name: "custom_bill", primaryKey: "id", statusField: "status" }
   statusValues: { closed: "CLOSED", open: "OPEN" }

6. 批处理说明：
   - 默认每批处理10条记录，可通过 updateOptions.batchSize 调整
   - 批处理可以避免一次性处理大量数据导致的性能问题
   - 每个批次的处理是并行的，但批次之间是串行的

7. 错误处理：
   - 单条记录更新失败不会影响其他记录
   - 最终会显示成功和失败的统计信息
   - 详细的错误信息会记录在控制台中
*/
