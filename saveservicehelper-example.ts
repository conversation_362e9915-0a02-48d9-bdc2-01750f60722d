/**
 * 使用 SaveServiceHelper.update 更新单据状态的示例
 * <AUTHOR>
 * @date 2025-08-01
 */
import { AbstractListPlugin } from "@cosmic/bos-core/kd/bos/list/plugin";
import { SetFilterEvent, FilterContainerSearchClickArgs } from "@cosmic/bos-core/kd/bos/form/events";
import { ItemClickEvent } from "@cosmic/bos-core/kd/bos/form/control/events";
import { EventObject } from "@cosmic/bos-script/java/util";
import { SaveServiceHelper } from "@cosmic/bos-core/kd/bos/servicehelper";
import { RequestContext } from "@cosmic/bos-core/kd/bos/context";

class MyPlugin extends AbstractListPlugin {

    setFilter(e: SetFilterEvent): void {
        super.setFilter(e);
        //...
    }

    filterContainerSearchClick(e: FilterContainerSearchClickArgs): void {
        super.filterContainerSearchClick(e);
        //...
    }

    itemClick(e: ItemClickEvent): void {
        super.itemClick(e);

        let key = e.getItemKey();
        if("yg_close" == key) {

            var listModel = this.getView().getModel();
            var listV = this.getView().getSelectedRows();
            console.log("选中行数量:", listV.size());

            if (!listV || listV.size() === 0) {
                plugin.getView().showMessage("请先选择要关闭的单据!");
                return;
            }

            // 去重处理
            let deduplicationResult = this.deduplicateSelectedRows(listV);
            
            if (deduplicationResult.uniqueIds.length === 0) {
                plugin.getView().showMessage("未找到有效的单据ID!");
                return;
            }

            // 确认操作
            let confirmMessage = `确定要关闭选中的 ${deduplicationResult.uniqueIds.length} 条单据吗？`;
            if (!plugin.getView().showConfirm(confirmMessage)) {
                return;
            }

            // 使用 SaveServiceHelper.update 更新单据状态
            this.updateBillStatusWithSaveService(deduplicationResult.uniqueIds);
        }
    }

    /**
     * 对选中行进行去重处理
     */
    private deduplicateSelectedRows(listV: any): {
        uniqueIds: string[],
        duplicateCount: number,
        originalCount: number
    } {
        let uniqueIds: string[] = [];
        let processedIds = new Set<string>();
        let duplicateCount = 0;

        for (var i = 0; i < listV.size(); i++) {
            let row = listV.get(i);
            let billId = row.getString("id"); // 请根据实际字段名修改
            
            if (billId) {
                if (processedIds.has(billId)) {
                    duplicateCount++;
                } else {
                    processedIds.add(billId);
                    uniqueIds.push(billId);
                }
            }
        }

        return {
            uniqueIds,
            duplicateCount,
            originalCount: listV.size()
        };
    }

    /**
     * 使用 SaveServiceHelper.update 更新单据状态
     */
    private updateBillStatusWithSaveService(billIds: string[]): void {
        try {
            // 显示加载提示
            plugin.getView().showWaitDialog("正在关闭单据，请稍候...");

            // 创建请求上下文
            let ctx = new RequestContext();

            // 构造更新数据 - 方法1：逐个更新
            this.updateBillsOneByOne(billIds, ctx);

            // 或者使用方法2：批量更新（如果支持）
            // this.updateBillsBatch(billIds, ctx);

        } catch (error) {
            plugin.getView().hideWaitDialog();
            console.error("更新单据状态时发生错误:", error);
            plugin.getView().showErrorMessage("更新失败: " + error.message);
        }
    }

    /**
     * 方法1：逐个更新单据状态
     */
    private updateBillsOneByOne(billIds: string[], ctx: RequestContext): void {
        let successCount = 0;
        let failCount = 0;
        let totalCount = billIds.length;

        // 递归处理每个单据
        const updateNext = (index: number) => {
            if (index >= billIds.length) {
                // 所有单据处理完成
                plugin.getView().hideWaitDialog();
                let message = `处理完成！成功: ${successCount} 条，失败: ${failCount} 条`;
                plugin.getView().showMessage(message);
                
                if (successCount > 0) {
                    plugin.getView().refresh(); // 刷新列表
                }
                return;
            }

            let billId = billIds[index];
            
            // 构造更新数据
            let updateData = {
                "id": billId,                    // 主键
                "status": "closed",              // 状态字段 - 请根据实际字段名修改
                "closeDate": new Date(),         // 关闭日期
                "operator": ctx.getUserId()      // 操作人
            };

            // 调用 SaveServiceHelper.update
            SaveServiceHelper.update(
                "YourEntityName",    // 实体名称 - 请替换为实际的实体名
                updateData,          // 更新数据
                (result: any) => {   // 成功回调
                    console.log(`单据 ${billId} 更新成功:`, result);
                    successCount++;
                    updateNext(index + 1); // 处理下一个
                },
                (error: any) => {    // 失败回调
                    console.error(`单据 ${billId} 更新失败:`, error);
                    failCount++;
                    updateNext(index + 1); // 继续处理下一个
                },
                ctx                  // 请求上下文
            );
        };

        // 开始处理第一个单据
        updateNext(0);
    }

    /**
     * 方法2：批量更新单据状态（如果SaveServiceHelper支持批量操作）
     */
    private updateBillsBatch(billIds: string[], ctx: RequestContext): void {
        // 构造批量更新数据
        let batchUpdateData = billIds.map(billId => ({
            "id": billId,
            "status": "closed",
            "closeDate": new Date(),
            "operator": ctx.getUserId()
        }));

        // 调用批量更新（具体API可能因版本而异）
        SaveServiceHelper.update(
            "YourEntityName",        // 实体名称
            batchUpdateData,         // 批量更新数据数组
            (result: any) => {       // 成功回调
                plugin.getView().hideWaitDialog();
                console.log("批量更新成功:", result);
                plugin.getView().showMessage(`成功关闭 ${billIds.length} 条单据!`);
                plugin.getView().refresh();
            },
            (error: any) => {        // 失败回调
                plugin.getView().hideWaitDialog();
                console.error("批量更新失败:", error);
                plugin.getView().showErrorMessage("批量更新失败: " + error.message);
            },
            ctx                      // 请求上下文
        );
    }

    /**
     * 方法3：使用更详细的更新参数
     */
    private updateBillsWithDetailedParams(billIds: string[], ctx: RequestContext): void {
        billIds.forEach((billId, index) => {
            // 构造详细的更新参数
            let updateParams = {
                // 主键信息
                "primaryKey": {
                    "id": billId
                },
                // 要更新的字段
                "updateFields": {
                    "status": "closed",           // 状态
                    "closeDate": new Date(),      // 关闭日期
                    "closeUser": ctx.getUserId(), // 关闭人
                    "closeRemark": "批量关闭"     // 关闭备注
                },
                // 更新选项
                "options": {
                    "validateBusiness": true,     // 是否进行业务验证
                    "triggerWorkflow": false,     // 是否触发工作流
                    "updateVersion": true         // 是否更新版本号
                }
            };

            SaveServiceHelper.update(
                "YourEntityName",
                updateParams,
                (result: any) => {
                    console.log(`单据 ${billId} 更新成功`);
                    // 如果是最后一个，刷新界面
                    if (index === billIds.length - 1) {
                        plugin.getView().hideWaitDialog();
                        plugin.getView().showMessage("所有单据更新完成!");
                        plugin.getView().refresh();
                    }
                },
                (error: any) => {
                    console.error(`单据 ${billId} 更新失败:`, error);
                },
                ctx
            );
        });
    }

    // Listen for menu button click events.
    registerListener(e: EventObject): void {
        super.registerListener(e);

        // 监听工具栏
        let toolbar = this.getView().getControl("toolbarap") as Toolbar;
        toolbar.addItemClickListener(this);
    }
}

let plugin = new MyPlugin();

export { plugin };
