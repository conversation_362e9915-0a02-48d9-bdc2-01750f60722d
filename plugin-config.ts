/**
 * 列表关闭插件配置文件
 * <AUTHOR>
 * @date 2025-08-01
 */

export interface PluginConfig {
    // 字段映射配置
    fields: {
        id: string;           // 主键字段名
        billNo: string;       // 单据号字段名
        status: string;       // 状态字段名
    };
    
    // 服务配置
    service: {
        name: string;         // 服务名称
        operation: string;    // 操作方法名
    };
    
    // 状态值配置
    statusValues: {
        closed: string;       // 关闭状态值
        closedDisplay: string; // 关闭状态显示值
    };
    
    // 消息配置
    messages: {
        noSelection: string;
        confirmClose: string;
        successMessage: string;
        errorMessage: string;
        loadingMessage: string;
        alreadyClosed: string;
        noValidIds: string;
        duplicateDetected: string;  // 检测到重复时的提示消息
    };

    // 去重配置
    deduplication: {
        enabled: boolean;           // 是否启用去重
        showDuplicateMessage: boolean; // 是否显示重复提示消息
        logDuplicates: boolean;     // 是否在控制台记录重复项
    };
}

// 默认配置
export const defaultConfig: PluginConfig = {
    fields: {
        id: "id",
        billNo: "billno", 
        status: "status"
    },
    service: {
        name: "YourBillService",
        operation: "closeBills"
    },
    statusValues: {
        closed: "closed",
        closedDisplay: "已关闭"
    },
    messages: {
        noSelection: "请先选择要关闭的单据!",
        confirmClose: "确定要关闭选中的 {count} 条单据吗？",
        successMessage: "成功关闭 {count} 条单据!",
        errorMessage: "关闭单据失败: {error}",
        loadingMessage: "正在关闭单据，请稍候...",
        alreadyClosed: "单据 {billNo} 已经是关闭状态，无法重复关闭",
        noValidIds: "未找到有效的单据ID!",
        duplicateDetected: "检测到 {duplicateCount} 条重复记录，已自动去重。将处理 {uniqueCount} 条唯一记录。"
    },
    deduplication: {
        enabled: true,              // 默认启用去重
        showDuplicateMessage: true, // 默认显示重复提示
        logDuplicates: true         // 默认记录重复项到控制台
    }
};

// 工具函数：格式化消息
export function formatMessage(template: string, params: { [key: string]: any }): string {
    let result = template;
    for (const key in params) {
        result = result.replace(new RegExp(`\\{${key}\\}`, 'g'), params[key]);
    }
    return result;
}

// 工具函数：去重ID数组
export function deduplicateIds(selectedRows: any, config: PluginConfig): {
    billIds: string[],
    billNos: string[],
    originalCount: number,
    uniqueCount: number,
    duplicateCount: number,
    duplicateIds: string[]
} {
    let billIds: string[] = [];
    let billNos: string[] = [];
    let processedIds = new Set<string>(); // 用于去重的Set
    let duplicateIds: string[] = []; // 记录重复的ID

    for (let i = 0; i < selectedRows.size(); i++) {
        let row = selectedRows.get(i);
        let billId = row.getString(config.fields.id);
        let billNo = row.getString(config.fields.billNo);

        if (billId) {
            if (config.deduplication.enabled && processedIds.has(billId)) {
                // 如果启用去重且ID已存在，记录为重复
                duplicateIds.push(billId);
                if (config.deduplication.logDuplicates) {
                    console.log(`发现重复ID: ${billId}, 单据号: ${billNo}`);
                }
            } else {
                // 添加新的ID
                processedIds.add(billId);
                billIds.push(billId);
                billNos.push(billNo || billId);
            }
        }
    }

    let originalCount = selectedRows.size();
    let uniqueCount = billIds.length;
    let duplicateCount = duplicateIds.length;

    // 如果启用了重复提示且有重复项，记录信息
    if (config.deduplication.enabled && config.deduplication.logDuplicates && duplicateCount > 0) {
        console.log(`去重统计: 原始 ${originalCount} 条，唯一 ${uniqueCount} 条，重复 ${duplicateCount} 条`);
    }

    return {
        billIds,
        billNos,
        originalCount,
        uniqueCount,
        duplicateCount,
        duplicateIds
    };
}

// 工具函数：数组去重（通用版本）
export function uniqueArray<T>(array: T[], keySelector?: (item: T) => string): T[] {
    if (!keySelector) {
        // 简单去重
        return [...new Set(array)];
    } else {
        // 基于键的去重
        const seen = new Set<string>();
        return array.filter(item => {
            const key = keySelector(item);
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }
}
