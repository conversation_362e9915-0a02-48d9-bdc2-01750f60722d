/**
 * 简化的去重示例 - 基于你的原始代码
 * <AUTHOR>
 * @date 2025-08-01
 */
import { AbstractListPlugin } from "@cosmic/bos-core/kd/bos/list/plugin";
import { SetFilterEvent, FilterContainerSearchClickArgs } from "@cosmic/bos-core/kd/bos/form/events";
import { ItemClickEvent } from "@cosmic/bos-core/kd/bos/form/control/events";
import { EventObject } from "@cosmic/bos-script/java/util";

class MyPlugin extends AbstractListPlugin {

    setFilter(e: SetFilterEvent): void {
        super.setFilter(e);
        //...
    }

    filterContainerSearchClick(e: FilterContainerSearchClickArgs): void {
        super.filterContainerSearchClick(e);
        //...
    }

    itemClick(e: ItemClickEvent): void {
        super.itemClick(e);

        let key = e.getItemKey();
        if("yg_close" == key) {

            var listModel = this.getView().getModel();
            var listV = this.getView().getSelectedRows();
            console.log("选中行数量:", listV.size());

            // 方法1: 使用Set进行去重
            let uniqueIds = new Set<string>();
            let uniqueBillNos = new Set<string>();
            let duplicateCount = 0;

            for(var i = 0; i < listV.size(); i++){
                let row = listV.get(i);
                let billId = row.getString("id"); // 或者你的实际ID字段名
                let billNo = row.getString("billno"); // 或者你的实际单据号字段名
                
                if (billId) {
                    let beforeSize = uniqueIds.size;
                    uniqueIds.add(billId);
                    
                    // 如果添加后Set大小没变，说明是重复的
                    if (uniqueIds.size === beforeSize) {
                        duplicateCount++;
                        console.log("发现重复ID:", billId);
                    } else {
                        // 只有非重复的才添加单据号
                        uniqueBillNos.add(billNo || billId);
                    }
                }
            }

            // 转换为数组
            let uniqueIdArray = Array.from(uniqueIds);
            let uniqueBillNoArray = Array.from(uniqueBillNos);

            console.log("原始选中数量:", listV.size());
            console.log("去重后ID数量:", uniqueIdArray.length);
            console.log("重复数量:", duplicateCount);
            console.log("去重后的IDs:", uniqueIdArray);

            if (uniqueIdArray.length === 0) {
                plugin.getView().showMessage("未找到有效的单据ID!");
                return;
            }

            // 如果有重复，提示用户
            if (duplicateCount > 0) {
                plugin.getView().showMessage(`检测到 ${duplicateCount} 条重复记录，已自动去重。将处理 ${uniqueIdArray.length} 条唯一记录。`);
            }

            // 这里调用你的关闭逻辑
            this.closeBillsWithUniqueIds(uniqueIdArray, uniqueBillNoArray);
        }
    }

    /**
     * 使用去重后的ID关闭单据
     */
    private closeBillsWithUniqueIds(billIds: string[], billNos: string[]): void {
        try {
            // 确认操作
            let confirmResult = plugin.getView().showConfirm(`确定要关闭 ${billIds.length} 条单据吗？`);
            if (!confirmResult) {
                return;
            }

            // 这里添加你的后端调用逻辑
            console.log("准备关闭的单据IDs:", billIds);
            console.log("准备关闭的单据号:", billNos);
            
            // 示例：调用后端服务
            // let params = {
            //     ids: billIds,
            //     billNos: billNos,
            //     status: "closed"
            // };
            // 
            // plugin.getView().invokeOperation("YourService", "closeBills", params, (result) => {
            //     if (result.isSuccess()) {
            //         plugin.getView().showMessage("成功关闭 " + billIds.length + " 条单据!");
            //         plugin.getView().refresh();
            //     } else {
            //         plugin.getView().showMessage("关闭失败: " + result.getMessage());
            //     }
            // });

            // 临时提示
            plugin.getView().showMessage(`已处理 ${billIds.length} 条唯一单据!`);

        } catch (error) {
            console.error("关闭单据时发生错误:", error);
            plugin.getView().showMessage("操作失败: " + error.message);
        }
    }

    // Listen for menu button click events.
    registerListener(e: EventObject):void {
        super.registerListener(e);

        // 监听工具栏
        let toolbar = this.getView().getControl("toolbarap") as Toolbar;
        toolbar.addItemClickListener(this);
    }
}

// 方法2: 通用的数组去重工具函数
function deduplicateArray<T>(array: T[], keySelector: (item: T) => string): {
    unique: T[],
    duplicates: T[],
    originalCount: number,
    uniqueCount: number
} {
    let seen = new Set<string>();
    let unique: T[] = [];
    let duplicates: T[] = [];

    for (let item of array) {
        let key = keySelector(item);
        if (seen.has(key)) {
            duplicates.push(item);
        } else {
            seen.add(key);
            unique.push(item);
        }
    }

    return {
        unique,
        duplicates,
        originalCount: array.length,
        uniqueCount: unique.length
    };
}

// 方法3: 简单的字符串数组去重
function deduplicateStringArray(array: string[]): string[] {
    return [...new Set(array)];
}

let plugin = new MyPlugin();

export { plugin };
