# 列表界面关闭按钮插件使用说明

## 功能概述
这个插件为kingscript列表界面提供了关闭按钮功能，可以：
- 获取用户选中的行
- 验证选中行是否可以关闭
- 批量更新单据状态为关闭
- 提供友好的用户交互和错误处理

## 文件结构
```
├── list-close-plugin.ts     # 主插件文件
├── plugin-config.ts         # 配置文件
└── usage-example.md         # 使用说明（本文件）
```

## 配置说明

### 1. 字段映射配置
在 `plugin-config.ts` 中修改字段映射：
```typescript
fields: {
    id: "fid",           // 主键字段名
    billNo: "fbillno",   // 单据号字段名
    status: "fstatus"    // 状态字段名
}
```

### 2. 服务配置
配置后端服务信息：
```typescript
service: {
    name: "YourBillService",    // 后端服务名
    operation: "closeBills"     // 关闭操作方法名
}
```

### 3. 状态值配置
配置状态相关的值：
```typescript
statusValues: {
    closed: "C",              // 关闭状态值（存储在数据库中的值）
    closedDisplay: "已关闭"   // 关闭状态显示值（界面显示的值）
}
```

### 4. 消息配置
自定义各种提示消息：
```typescript
messages: {
    noSelection: "请先选择要关闭的单据!",
    confirmClose: "确定要关闭选中的 {count} 条单据吗？",
    successMessage: "成功关闭 {count} 条单据!",
    // ... 其他消息
}
```

## 使用方法

### 1. 基本使用
直接使用默认配置：
```typescript
import { plugin } from "./list-close-plugin";
// 插件会自动注册到列表界面
```

### 2. 自定义配置使用
```typescript
import { MyPlugin } from "./list-close-plugin";

let customPlugin = new MyPlugin({
    fields: {
        id: "fid",
        billNo: "fbillno", 
        status: "fstatus"
    },
    service: {
        name: "MyBillService",
        operation: "closeBills"
    },
    statusValues: {
        closed: "C",
        closedDisplay: "已关闭"
    }
});

export { customPlugin as plugin };
```

## 后端服务接口

后端服务需要实现以下接口：

### 请求参数
```json
{
    "ids": ["id1", "id2", "id3"],           // 单据ID数组
    "billNos": ["bill001", "bill002"],      // 单据号数组
    "status": "closed",                     // 目标状态
    "closeDate": "2025-08-01T10:30:00Z",   // 关闭时间
    "operator": "user123"                   // 操作人
}
```

### 返回结果
```json
{
    "success": true,
    "message": "操作成功",
    "data": {
        "successCount": 3,
        "failedCount": 0
    }
}
```

## 工具栏按钮配置

确保在列表界面的工具栏中添加关闭按钮：
```xml
<toolbar id="toolbarap">
    <button key="yg_close" text="关闭" />
</toolbar>
```

## 注意事项

1. **字段名映射**：确保配置中的字段名与实际数据模型中的字段名一致
2. **权限控制**：可以在 `validateSelectedRows` 方法中添加权限验证逻辑
3. **业务规则**：可以根据业务需求在验证方法中添加更多检查规则
4. **错误处理**：插件已包含完整的错误处理，包括网络错误和业务错误
5. **用户体验**：提供了加载提示、确认对话框等用户友好的交互

## 扩展功能

### 添加权限验证
```typescript
private validateSelectedRows(selectedRows: any): { valid: boolean, message?: string } {
    // 检查用户权限
    if (!this.hasClosePermission()) {
        return {
            valid: false,
            message: "您没有关闭单据的权限"
        };
    }
    
    // 原有的状态验证逻辑...
}

private hasClosePermission(): boolean {
    // 实现权限检查逻辑
    return true;
}
```

### 添加业务规则验证
```typescript
private validateSelectedRows(selectedRows: any): { valid: boolean, message?: string } {
    for (let i = 0; i < selectedRows.size(); i++) {
        let row = selectedRows.get(i);
        
        // 检查是否有未完成的子单据
        let hasUnfinishedSubBills = row.getBoolean("hasUnfinishedSubBills");
        if (hasUnfinishedSubBills) {
            let billNo = row.getString(this.config.fields.billNo);
            return {
                valid: false,
                message: `单据 ${billNo} 存在未完成的子单据，无法关闭`
            };
        }
    }
    
    return { valid: true };
}
```
