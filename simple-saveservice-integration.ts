/**
 * 简化版本 - 直接在你的原始代码中集成 SaveServiceHelper.update
 * <AUTHOR>
 * @date 2025-08-01
 */
import { AbstractListPlugin } from "@cosmic/bos-core/kd/bos/list/plugin";
import { SetFilterEvent, FilterContainerSearchClickArgs } from "@cosmic/bos-core/kd/bos/form/events";
import { ItemClickEvent } from "@cosmic/bos-core/kd/bos/form/control/events";
import { EventObject } from "@cosmic/bos-script/java/util";
import { SaveServiceHelper } from "@cosmic/bos-core/kd/bos/servicehelper";
import { RequestContext } from "@cosmic/bos-core/kd/bos/context";

class MyPlugin extends AbstractListPlugin {

    setFilter(e: SetFilterEvent): void {
        super.setFilter(e);
        //...
    }

    filterContainerSearchClick(e: FilterContainerSearchClickArgs): void {
        super.filterContainerSearchClick(e);
        //...
    }

    itemClick(e: ItemClickEvent): void {
        super.itemClick(e);

        let key = e.getItemKey();
        if("yg_close" == key) {

            var listModel = this.getView().getModel();
            var listV = this.getView().getSelectedRows();
            console.log(listV);

            if (!listV || listV.size() === 0) {
                plugin.getView().showMessage("请先选择要关闭的单据!");
                return;
            }

            // 确认操作
            if (!plugin.getView().showConfirm(`确定要关闭选中的 ${listV.size()} 条单据吗？`)) {
                return;
            }

            // 去重并收集ID
            let uniqueIds = new Set<string>();
            for(var i = 0; i < listV.size(); i++){
                let row = listV.get(i);
                let billId = row.getString("id"); // 请根据实际字段名修改
                if (billId) {
                    uniqueIds.add(billId);
                }
            }

            let uniqueIdArray = Array.from(uniqueIds);
            if (uniqueIdArray.length === 0) {
                plugin.getView().showMessage("未找到有效的单据ID!");
                return;
            }

            console.log("准备关闭的单据IDs:", uniqueIdArray);

            // 使用 SaveServiceHelper.update 更新状态
            this.updateBillStatus(uniqueIdArray);
        }
    }

    /**
     * 使用 SaveServiceHelper.update 更新单据状态
     */
    private updateBillStatus(billIds: string[]): void {
        try {
            plugin.getView().showWaitDialog("正在关闭单据，请稍候...");

            let ctx = new RequestContext();
            let successCount = 0;
            let totalCount = billIds.length;

            // 逐个更新单据状态
            billIds.forEach((billId, index) => {
                // 构造更新数据
                let updateData = {
                    "id": billId,                    // 主键字段
                    "status": "closed"               // 状态字段 - 请根据实际字段名修改
                    // 可以添加更多字段：
                    // "closeDate": new Date(),
                    // "closeUser": ctx.getUserId()
                };

                // 调用 SaveServiceHelper.update
                SaveServiceHelper.update(
                    "YourEntityName",    // 实体名称 - 请替换为实际的实体名，如 "bd_material", "ap_bill" 等
                    updateData,
                    // 成功回调
                    (result: any) => {
                        console.log(`单据 ${billId} 关闭成功`);
                        successCount++;
                        
                        // 如果是最后一个单据，显示完成消息
                        if (index === totalCount - 1) {
                            plugin.getView().hideWaitDialog();
                            plugin.getView().showMessage(`成功关闭 ${successCount} 条单据!`);
                            plugin.getView().refresh(); // 刷新列表
                        }
                    },
                    // 失败回调
                    (error: any) => {
                        console.error(`单据 ${billId} 关闭失败:`, error);
                        
                        // 如果是最后一个单据，显示完成消息
                        if (index === totalCount - 1) {
                            plugin.getView().hideWaitDialog();
                            let failCount = totalCount - successCount;
                            plugin.getView().showMessage(`处理完成！成功: ${successCount} 条，失败: ${failCount} 条`);
                            if (successCount > 0) {
                                plugin.getView().refresh();
                            }
                        }
                    },
                    ctx  // 请求上下文
                );
            });

        } catch (error) {
            plugin.getView().hideWaitDialog();
            console.error("更新单据状态时发生错误:", error);
            plugin.getView().showErrorMessage("操作失败: " + error.message);
        }
    }

    // Listen for menu button click events.
    registerListener(e: EventObject): void {
        super.registerListener(e);

        // 监听工具栏
        let toolbar = this.getView().getControl("toolbarap") as Toolbar;
        toolbar.addItemClickListener(this);
    }
}

let plugin = new MyPlugin();

export { plugin };

/*
使用说明：

1. 替换实体名称：
   将 "YourEntityName" 替换为你的实际实体名称，例如：
   - "bd_material" (物料)
   - "ap_bill" (应付单)
   - "ar_bill" (应收单)
   - 等等

2. 替换字段名：
   - "id" 替换为实际的主键字段名，如 "fid"
   - "status" 替换为实际的状态字段名，如 "fstatus", "fbillstatus" 等

3. 添加更多更新字段：
   在 updateData 中可以添加更多需要更新的字段：
   {
       "id": billId,
       "status": "closed",
       "closeDate": new Date(),
       "closeUser": ctx.getUserId(),
       "closeRemark": "批量关闭"
   }

4. SaveServiceHelper.update 的基本语法：
   SaveServiceHelper.update(
       entityName,      // 实体名称
       updateData,      // 更新数据对象
       successCallback, // 成功回调函数
       errorCallback,   // 失败回调函数
       context         // 请求上下文（可选）
   );

5. 常见的实体名称和字段名：
   - 单据类：通常主键为 "fid"，状态字段为 "fbillstatus"
   - 基础资料：通常主键为 "fid"，状态字段为 "fstatus"
   - 自定义实体：根据实际设计确定

示例配置：
如果你的单据实体名为 "your_bill"，主键为 "fid"，状态字段为 "fbillstatus"：

SaveServiceHelper.update(
    "your_bill",
    {
        "fid": billId,
        "fbillstatus": "C"  // C表示关闭状态
    },
    successCallback,
    errorCallback,
    ctx
);
*/
